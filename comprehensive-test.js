/**
 * Teste Abrangente - Validação Completa das Refatorações Fase 1
 * Verifica todas as implementações e funcionalidades refatoradas
 */

const fs = require('fs');
const path = require('path');

console.log('🔬 TESTE ABRANGENTE - VALIDAÇÃO FASE 1');
console.log('=====================================\n');

let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0,
    details: []
};

// Função auxiliar para logging de resultados
function logResult(testName, status, message, details = null) {
    const symbols = { pass: '✅', fail: '❌', warn: '⚠️' };
    console.log(`${symbols[status]} ${testName}: ${message}`);
    
    if (details) {
        console.log(`   📋 ${details}`);
    }
    
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
    testResults.details.push({ testName, status, message, details });
}

// Função para verificar se arquivo contém texto específico
function fileContains(filePath, searchText, description) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        return content.includes(searchText);
    } catch (error) {
        logResult(description, 'fail', `Erro ao ler arquivo ${filePath}: ${error.message}`);
        return false;
    }
}

// Função para contar ocorrências em arquivo
function countOccurrences(filePath, searchText) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        return (content.match(new RegExp(searchText, 'g')) || []).length;
    } catch (error) {
        return 0;
    }
}

console.log('1️⃣ TESTE: CONFIGURAÇÕES CONSOLIDADAS');
console.log('-----------------------------------');

// 1.1 Verificar estruturas centralizadas em config.js
try {
    const config = require('./src/config/config');
    
    // Verificar se as novas estruturas existem
    if (config.INTERVALS && config.LIMITS && config.TIMEOUTS) {
        logResult('Estruturas Centralizadas', 'pass', 'INTERVALS, LIMITS e TIMEOUTS criadas');
    } else {
        logResult('Estruturas Centralizadas', 'fail', 'Estruturas principais não encontradas');
    }
    
    // Verificar valores específicos
    const expectedIntervals = {
        SAVE: 5000,
        RETRY_SAVE: 10000,
        CLEANUP: 24 * 60 * 60 * 1000,
        STATS_SAVE: 5 * 60 * 1000,
        HEARTBEAT: 60 * 1000
    };
    
    let intervalCheck = true;
    for (const [key, expectedValue] of Object.entries(expectedIntervals)) {
        if (config.INTERVALS[key] !== expectedValue) {
            intervalCheck = false;
            break;
        }
    }
    
    if (intervalCheck) {
        logResult('Valores INTERVALS', 'pass', 'Todos os valores estão corretos');
    } else {
        logResult('Valores INTERVALS', 'fail', 'Valores incorretos ou ausentes');
    }
    
    // Verificar compatibilidade
    if (config.SAVE_INTERVAL === config.INTERVALS.SAVE && 
        config.RETRY_SAVE_INTERVAL === config.INTERVALS.RETRY_SAVE) {
        logResult('Compatibilidade', 'pass', 'Configurações de compatibilidade funcionando');
    } else {
        logResult('Compatibilidade', 'fail', 'Problemas na compatibilidade');
    }
    
} catch (error) {
    logResult('Config Import', 'fail', `Erro ao importar config: ${error.message}`);
}

// 1.2 Verificar eliminação de duplicações
const configContent = fs.readFileSync('./src/config/config.js', 'utf8');
if (!configContent.includes('CONVERSATION_CONFIG')) {
    logResult('Eliminação CONVERSATION_CONFIG', 'pass', 'CONVERSATION_CONFIG removido com sucesso');
} else {
    logResult('Eliminação CONVERSATION_CONFIG', 'fail', 'CONVERSATION_CONFIG ainda existe');
}

// 1.3 Verificar uso das novas configurações nos arquivos dependentes
const filesToCheck = [
    { file: './index.js', config: 'INTERVALS.STATS_SAVE', desc: 'index.js usa INTERVALS' },
    { file: './src/core/conversation.js', config: 'INTERVALS.SAVE', desc: 'conversation.js usa INTERVALS' },
    { file: './src/utils/healthMonitor.js', config: 'INTERVALS.HEALTH_CHECK', desc: 'healthMonitor.js usa INTERVALS' },
    { file: './src/services/whatsapp.js', config: 'TIMEOUTS.CONNECT', desc: 'whatsapp.js usa TIMEOUTS' },
    { file: './src/services/openai.js', config: 'LIMITS.MAX_RESPONSE_TOKENS', desc: 'openai.js usa LIMITS' }
];

filesToCheck.forEach(({ file, config, desc }) => {
    if (fileContains(file, config, desc)) {
        logResult('Uso Configurações', 'pass', desc);
    } else {
        logResult('Uso Configurações', 'fail', `${desc} - não encontrado`);
    }
});

console.log('\n2️⃣ TESTE: ERRORHANDLER CENTRALIZADO');
console.log('----------------------------------');

// 2.1 Verificar se ErrorHandler existe e funciona
try {
    const ErrorHandler = require('./src/utils/errorHandler');
    
    // Testar métodos principais
    const testError = new Error('Teste');
    const result = ErrorHandler.handle(testError, 'TestContext');
    
    if (result && result.message && result.context && result.timestamp) {
        logResult('ErrorHandler Básico', 'pass', 'Método handle() funcionando');
    } else {
        logResult('ErrorHandler Básico', 'fail', 'Método handle() com problemas');
    }
    
    // Testar método crítico
    if (typeof ErrorHandler.handleCritical === 'function') {
        logResult('ErrorHandler Crítico', 'pass', 'Método handleCritical() existe');
    } else {
        logResult('ErrorHandler Crítico', 'fail', 'Método handleCritical() não encontrado');
    }
    
    // Testar método de validação
    if (typeof ErrorHandler.handleValidationError === 'function') {
        logResult('ErrorHandler Validação', 'pass', 'Método handleValidationError() existe');
    } else {
        logResult('ErrorHandler Validação', 'fail', 'Método handleValidationError() não encontrado');
    }
    
} catch (error) {
    logResult('ErrorHandler Import', 'fail', `Erro ao importar ErrorHandler: ${error.message}`);
}

// 2.2 Verificar substituição de trackEvent('error') por ErrorHandler
const filesToCheckErrorHandling = [
    './index.js',
    './src/core/message-handler.js',
    './src/utils/healthMonitor.js'
];

filesToCheckErrorHandling.forEach(file => {
    const errorHandlerUsage = countOccurrences(file, 'ErrorHandler\\.');
    const oldTrackEventUsage = countOccurrences(file, "trackEvent\\('error'");
    
    if (errorHandlerUsage > 0 && oldTrackEventUsage === 0) {
        logResult('Migração ErrorHandler', 'pass', `${path.basename(file)} migrado corretamente`);
    } else if (errorHandlerUsage > 0 && oldTrackEventUsage > 0) {
        logResult('Migração ErrorHandler', 'warn', `${path.basename(file)} parcialmente migrado`);
    } else {
        logResult('Migração ErrorHandler', 'fail', `${path.basename(file)} não migrado`);
    }
});

console.log('\n3️⃣ TESTE: CRIAÇÃO DE DIRETÓRIOS PADRONIZADA');
console.log('------------------------------------------');

// 3.1 Verificar novos métodos no FileManager
try {
    const fileManager = require('./src/utils/fileManager');
    
    if (typeof fileManager.ensureDirectoryExists === 'function') {
        logResult('FileManager Método', 'pass', 'ensureDirectoryExists() existe');
    } else {
        logResult('FileManager Método', 'fail', 'ensureDirectoryExists() não encontrado');
    }
    
    if (typeof fileManager.ensureMultipleDirectories === 'function') {
        logResult('FileManager Múltiplos', 'pass', 'ensureMultipleDirectories() existe');
    } else {
        logResult('FileManager Múltiplos', 'fail', 'ensureMultipleDirectories() não encontrado');
    }
    
} catch (error) {
    logResult('FileManager Import', 'fail', `Erro ao importar FileManager: ${error.message}`);
}

// 3.2 Verificar remoção de lógica duplicada
const indexContent = fs.readFileSync('./index.js', 'utf8');
const backupContent = fs.readFileSync('./src/services/backupService.js', 'utf8');

// Verificar se index.js não tem mais fs.mkdirSync
if (!indexContent.includes('fs.mkdirSync')) {
    logResult('Remoção Duplicação Index', 'pass', 'fs.mkdirSync removido do index.js');
} else {
    logResult('Remoção Duplicação Index', 'fail', 'fs.mkdirSync ainda existe no index.js');
}

// Verificar se backupService usa fileManager
if (backupContent.includes('fileManager.ensureDirectoryExists')) {
    logResult('BackupService Migração', 'pass', 'backupService.js usa fileManager centralizado');
} else {
    logResult('BackupService Migração', 'fail', 'backupService.js não migrado');
}

console.log('\n4️⃣ TESTE: FUNCIONALIDADE PRESERVADA');
console.log('----------------------------------');

// 4.0 Teste de simulação de operações principais
try {
    // Simular criação de diretório
    const fileManager = require('./src/utils/fileManager');
    const testDir = path.join(__dirname, 'test-temp-dir');

    // Teste assíncrono básico
    (async () => {
        try {
            await fileManager.ensureDirectoryExists(testDir, false);
            if (fs.existsSync(testDir)) {
                logResult('Simulação FileManager', 'pass', 'Criação de diretório funcionando');
                fs.rmSync(testDir, { recursive: true, force: true });
            } else {
                logResult('Simulação FileManager', 'fail', 'Diretório não foi criado');
            }
        } catch (error) {
            logResult('Simulação FileManager', 'fail', `Erro na simulação: ${error.message}`);
        }
    })();

    // Simular ErrorHandler
    const ErrorHandler = require('./src/utils/errorHandler');
    const testResult = ErrorHandler.safeExecute(async () => {
        return 'teste bem-sucedido';
    }, 'TestSimulation');

    if (testResult) {
        logResult('Simulação ErrorHandler', 'pass', 'safeExecute funcionando');
    }

    // Simular configurações
    const config = require('./src/config/config');
    if (config.INTERVALS.SAVE === 5000 && config.LIMITS.MAX_RESPONSE_TOKENS === 400) {
        logResult('Simulação Config', 'pass', 'Configurações acessíveis e corretas');
    } else {
        logResult('Simulação Config', 'fail', 'Problemas no acesso às configurações');
    }

} catch (error) {
    logResult('Simulação Geral', 'fail', `Erro na simulação: ${error.message}`);
}

// 4.1 Testar imports de todos os módulos principais
const modulesToTest = [
    { path: './src/config/config', name: 'Config' },
    { path: './src/core/conversation', name: 'Conversation' },
    { path: './src/core/message-handler', name: 'MessageHandler' },
    { path: './src/services/whatsapp', name: 'WhatsApp' },
    { path: './src/services/openai', name: 'OpenAI' },
    { path: './src/services/backupService', name: 'BackupService' },
    { path: './src/utils/healthMonitor', name: 'HealthMonitor' },
    { path: './src/utils/fileManager', name: 'FileManager' },
    { path: './src/utils/eventTracker', name: 'EventTracker' },
    { path: './src/utils/errorHandler', name: 'ErrorHandler' }
];

modulesToTest.forEach(({ path: modulePath, name }) => {
    try {
        require(modulePath);
        logResult('Import Módulo', 'pass', `${name} importado com sucesso`);
    } catch (error) {
        logResult('Import Módulo', 'fail', `${name} falhou: ${error.message}`);
    }
});

console.log('\n5️⃣ TESTE: IMPORTS E DEPENDÊNCIAS');
console.log('--------------------------------');

// 5.1 Verificar se todos os arquivos têm os imports corretos
const importChecks = [
    { file: './index.js', import: 'ErrorHandler', desc: 'index.js importa ErrorHandler' },
    { file: './src/core/conversation.js', import: 'ErrorHandler', desc: 'conversation.js importa ErrorHandler' },
    { file: './src/utils/healthMonitor.js', import: 'INTERVALS, LIMITS', desc: 'healthMonitor.js importa configurações' },
    { file: './src/services/backupService.js', import: 'fileManager', desc: 'backupService.js importa fileManager' }
];

importChecks.forEach(({ file, import: importText, desc }) => {
    if (fileContains(file, importText, desc)) {
        logResult('Import Correto', 'pass', desc);
    } else {
        logResult('Import Correto', 'fail', `${desc} - não encontrado`);
    }
});

console.log('\n📊 RELATÓRIO FINAL');
console.log('==================');
console.log(`✅ Testes Aprovados: ${testResults.passed}`);
console.log(`❌ Testes Falharam: ${testResults.failed}`);
console.log(`⚠️  Avisos: ${testResults.warnings}`);
console.log(`📈 Taxa de Sucesso: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

if (testResults.failed === 0) {
    console.log('\n🎉 TODAS AS REFATORAÇÕES VALIDADAS COM SUCESSO!');
    console.log('A Fase 1 foi implementada corretamente e está funcionando como esperado.');
} else {
    console.log('\n⚠️  PROBLEMAS ENCONTRADOS:');
    testResults.details
        .filter(result => result.status === 'fail')
        .forEach(result => {
            console.log(`   • ${result.testName}: ${result.message}`);
        });
}

if (testResults.warnings > 0) {
    console.log('\n📋 AVISOS:');
    testResults.details
        .filter(result => result.status === 'warn')
        .forEach(result => {
            console.log(`   • ${result.testName}: ${result.message}`);
        });
}

process.exit(testResults.failed > 0 ? 1 : 0);
