const fs = require('fs');
const path = require('path');
const { conversationHistoryFile } = require('../config/config');
const fileManager = require('../utils/fileManager');
const ErrorHandler = require('../utils/errorHandler');

class BackupService {
    constructor() {
        this.backupDir = path.join(process.cwd(), 'data', 'backups');
        this.init();
    }

    async init() {
        // Usa o fileManager centralizado para criar diretório
        await fileManager.ensureDirectoryExists(this.backupDir);

        // Configura o backup no encerramento
        process.on('SIGTERM', () => this.handleShutdown());
        process.on('SIGINT', () => this.handleShutdown());

        console.log('✅ Backup Service inicializado');
    }

    async handleShutdown() {
        console.log('🔄 Iniciando backup de encerramento...');
        try {
            const fileName = path.basename(conversationHistoryFile);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = path.join(this.backupDir, `${fileName}.${timestamp}.bak`);

            await fs.promises.copyFile(conversationHistoryFile, backupPath);
            console.log('✅ Backup de encerramento concluído');
            process.exit(0);
        } catch (error) {
            ErrorHandler.handleCritical(error, 'BackupShutdown', true);
        }
    }
}

module.exports = new BackupService();