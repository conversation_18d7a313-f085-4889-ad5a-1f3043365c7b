const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Validação de configurações
const validateEnvVars = (requiredVars) => {
    requiredVars.forEach(varName => {
        if (!process.env[varName]) {
            console.error(`${varName} não configurada no .env`);
            throw new Error(`${varName} não configurada.`);
        }
    });
};

// Verificação inicial
validateEnvVars(['BOT_NUMBER', 'BOT_NAME', 'OPENAI_API_KEY']);

// Caminhos do sistema
const rootDir = path.join(__dirname, '..', '..');
const dataDir = path.join(rootDir, 'data');
const authDir = path.join(rootDir, 'auth');

// Nota: Criação de diretórios agora é gerenciada pelo fileManager centralizado

// Configurações centralizadas do sistema
const INTERVALS = {
    SAVE: 5000, // 5 segundos
    RETRY_SAVE: 10000, // 10 segundos
    CLEANUP: 24 * 60 * 60 * 1000, // 24 horas em milissegundos
    STATS_SAVE: 5 * 60 * 1000, // 5 minutos
    HEARTBEAT: 60 * 1000, // 1 minuto
    HEALTH_CHECK: 5 * 60 * 1000, // 5 minutos
    MEMORY_CHECK: 10 * 60 * 1000, // 10 minutos
};

const LIMITS = {
    MAX_HISTORY_MESSAGES: 10,
    MAX_RESPONSE_TOKENS: 400,
    MAX_STORED_BOT_MESSAGE_IDS: 1000,
    MAX_HISTORY_PER_USER: 100,
    MAX_RECONNECT_ATTEMPTS: 10,
    MAX_MEMORY_USAGE_MB: 1024, // 1GB
    MAX_INACTIVITY_TIME: 15 * 60 * 1000, // 15 minutos
};

const TIMEOUTS = {
    CONNECT: 60_000, // 60 segundos
    QR: 40_000, // 40 segundos
    QUERY: 60_000, // 60 segundos
};

// Exportação de configurações
module.exports = {
    // Variáveis de ambiente
    botNumber: process.env.BOT_NUMBER,
    botName: process.env.BOT_NAME,
    openaiApiKey: process.env.OPENAI_API_KEY,
    openaiModel: process.env.OPENAI_MODEL || 'gpt-4',

    // Caminhos de arquivos
    conversationHistoryFile: path.join(dataDir, 'conversationHistory.json'),
    authPath: authDir,

    // Configurações organizadas
    INTERVALS,
    LIMITS,
    TIMEOUTS,

    // Compatibilidade com código existente (DEPRECATED - usar INTERVALS/LIMITS)
    SAVE_INTERVAL: INTERVALS.SAVE,
    RETRY_SAVE_INTERVAL: INTERVALS.RETRY_SAVE,
    HISTORY_CLEANUP_INTERVAL: INTERVALS.CLEANUP,
    MAX_HISTORY_MESSAGES: LIMITS.MAX_HISTORY_MESSAGES,
    MAX_RESPONSE_TOKENS: LIMITS.MAX_RESPONSE_TOKENS,
    MAX_STORED_BOT_MESSAGE_IDS: LIMITS.MAX_STORED_BOT_MESSAGE_IDS,
    MAX_RECONNECT_ATTEMPTS: LIMITS.MAX_RECONNECT_ATTEMPTS,
    MAX_HISTORY_PER_USER: LIMITS.MAX_HISTORY_PER_USER,

    // Função para obter o prompt do sistema
    getSystemPrompt: (isGroup) => {
        return {
            role: 'system',
            content: `Você é ${module.exports.botName}, uma pessoa comum que conversa pelo WhatsApp. Siga estas orientações:

1. PERSONALIDADE:
   - Seja amigável e descontraído, evitando formalidades
   - Use linguagem coloquial brasileira e gírias ocasionais
   - Inclua emojis com moderação para expressar emoções
   - Mantenha respostas curtas e diretas, como em conversas reais

2. ESTILO:
   - Varie o tamanho e tom das respostas para parecer natural
   - Use "kkk", "rs" ou "haha" quando apropriado
   - Faça perguntas para manter a conversa fluindo
   - Seja espontâneo e conversável

3. EVITE:
   - Linguagem muito formal ou técnica
   - Respostas longas com muitos detalhes
   - Estruturas repetitivas

EXEMPLOS:
- "aí, tudo certo? tava ocupado aqui kkk oq manda?"
- "nossa, nem sei te dizer... talvez? 🤔"
- "tô por aqui sim, só tava almoçando. me conta, como foi o fds?"
- "agora não posso, te chamo mais tarde blz?"

Quando perguntarem sobre você, crie detalhes plausíveis de uma pessoa comum que mora no Brasil.`
        };
    }
};