const { trackEvent } = require('./eventTracker');
const ErrorHandler = require('./errorHandler');
const { INTERVALS, LIMITS } = require('../config/config');

class HealthMonitor {
    constructor() {
        this.lastHeartbeat = Date.now();
        this.isHealthy = true;
        this.checkInterval = INTERVALS.HEALTH_CHECK;
        this.maxInactivity = LIMITS.MAX_INACTIVITY_TIME;
    }

    start() {
        // Inicia o monitoramento periódico
        setInterval(() => this.checkHealth(), this.checkInterval);

        // Monitora eventos do processo
        process.on('SIGTERM', () => this.handleShutdown('SIGTERM'));
        process.on('SIGINT', () => this.handleShutdown('SIGINT'));

        // Monitora uso de memória
        setInterval(() => this.checkMemoryUsage(), INTERVALS.MEMORY_CHECK);

        console.log('✅ Health Monitor iniciado');
    }

    updateHeartbeat() {
        this.lastHeartbeat = Date.now();
        this.isHealthy = true;
    }

    checkHealth() {
        const now = Date.now();
        const timeSinceLastHeartbeat = now - this.lastHeartbeat;

        if (timeSinceLastHeartbeat > this.maxInactivity) {
            this.isHealthy = false;
            ErrorHandler.handle(
                new Error('Aplicação inativa por muito tempo'),
                'HealthMonitor',
                { timeInactive: timeSinceLastHeartbeat }
            );

            // Tenta recuperar a aplicação
            this.recover();
        }
    }

    checkMemoryUsage() {
        const used = process.memoryUsage();
        const memoryUsage = {
            heapUsed: Math.round(used.heapUsed / 1024 / 1024),
            heapTotal: Math.round(used.heapTotal / 1024 / 1024),
            external: Math.round(used.external / 1024 / 1024)
        };

        // Alerta se o uso de memória estiver muito alto
        if (memoryUsage.heapUsed > LIMITS.MAX_MEMORY_USAGE_MB) {
            ErrorHandler.handle(
                new Error('Uso de memória alto detectado'),
                'MemoryMonitor',
                { memoryUsage }
            );
            
            // Força coleta de lixo
            if (global.gc) {
                global.gc();
            }
        }
    }

    async recover() {
        try {
            // Tenta reconectar ao WhatsApp
            const whatsapp = require('../services/whatsapp');
            await whatsapp.connect();
            
            // Atualiza o heartbeat após recuperação bem-sucedida
            this.updateHeartbeat();
            
            trackEvent('message', {
                type: 'recovery',
                message: 'Aplicação recuperada com sucesso'
            });
        } catch (error) {
            ErrorHandler.handleCritical(error, 'HealthRecovery', true);
        }
    }

    handleShutdown(signal) {
        trackEvent('message', {
            type: 'shutdown',
            message: `Recebido sinal ${signal}, encerrando aplicação...`
        });
        
        // Limpa recursos e encerra
        process.exit(0);
    }
}

module.exports = new HealthMonitor(); 