const { trackEvent } = require('./eventTracker');

/**
 * Centralizador de tratamento de erros do sistema
 * Padroniza logging, tracking e recuperação de erros
 */
class ErrorHandler {
    /**
     * Trata erro de forma padronizada
     * @param {Error|string} error - Erro a ser tratado
     * @param {string} context - Contexto onde o erro ocorreu
     * @param {Object} additionalData - Dados adicionais para logging
     * @returns {Object} Dados estruturados do erro
     */
    static handle(error, context = 'Unknown', additionalData = {}) {
        const errorData = this.formatError(error, context, additionalData);
        
        // Log estruturado no console
        console.error(`[${context}] Erro:`, {
            message: errorData.message,
            timestamp: errorData.timestamp,
            ...additionalData
        });
        
        // Registra no sistema de métricas
        trackEvent('error', errorData);
        
        return errorData;
    }

    /**
     * Trata erro crítico que pode exigir shutdown
     * @param {Error|string} error - Erro crítico
     * @param {string} context - Contexto do erro
     * @param {boolean} shouldExit - Se deve encerrar o processo
     */
    static handleCritical(error, context = 'Critical', shouldExit = false) {
        const errorData = this.handle(error, context, { critical: true });
        
        console.error('🚨 ERRO CRÍTICO DETECTADO:', errorData.message);
        
        if (shouldExit) {
            console.error('Encerrando aplicação devido a erro crítico...');
            process.exit(1);
        }
        
        return errorData;
    }

    /**
     * Trata erro de conexão/rede
     * @param {Error} error - Erro de conexão
     * @param {string} service - Serviço que falhou
     * @param {number} attempt - Tentativa atual
     */
    static handleConnectionError(error, service = 'Unknown Service', attempt = 1) {
        return this.handle(error, `Connection-${service}`, {
            type: 'connection',
            service,
            attempt,
            retryable: true
        });
    }

    /**
     * Trata erro de validação de dados
     * @param {string} message - Mensagem de erro
     * @param {Object} invalidData - Dados que falharam na validação
     * @param {string} context - Contexto da validação
     */
    static handleValidationError(message, invalidData = {}, context = 'Validation') {
        return this.handle(new Error(message), context, {
            type: 'validation',
            invalidData: this.sanitizeData(invalidData)
        });
    }

    /**
     * Wrapper para execução segura de funções assíncronas
     * @param {Function} asyncFn - Função assíncrona a ser executada
     * @param {string} context - Contexto da execução
     * @param {*} fallbackValue - Valor retornado em caso de erro
     */
    static async safeExecute(asyncFn, context = 'SafeExecute', fallbackValue = null) {
        try {
            return await asyncFn();
        } catch (error) {
            this.handle(error, context);
            return fallbackValue;
        }
    }

    /**
     * Formata erro em estrutura padronizada
     * @private
     */
    static formatError(error, context, additionalData) {
        const isErrorObject = error instanceof Error;
        
        return {
            message: isErrorObject ? error.message : String(error),
            stack: isErrorObject ? error.stack : new Error(String(error)).stack,
            context,
            timestamp: new Date().toISOString(),
            type: error.name || 'Error',
            code: error.code || null,
            ...additionalData
        };
    }

    /**
     * Remove dados sensíveis para logging seguro
     * @private
     */
    static sanitizeData(data) {
        if (!data || typeof data !== 'object') return data;
        
        const sanitized = { ...data };
        const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth'];
        
        Object.keys(sanitized).forEach(key => {
            if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
                sanitized[key] = '[REDACTED]';
            }
        });
        
        return sanitized;
    }

    /**
     * Configura handlers globais de erro
     */
    static setupGlobalHandlers() {
        // Erros não capturados
        process.on('uncaughtException', (error) => {
            this.handleCritical(error, 'UncaughtException', true);
        });

        // Promises rejeitadas não tratadas
        process.on('unhandledRejection', (reason, promise) => {
            this.handleCritical(reason, 'UnhandledRejection', false);
        });

        // Avisos do processo
        process.on('warning', (warning) => {
            this.handle(warning, 'ProcessWarning', { type: 'warning' });
        });

        console.log('✅ Handlers globais de erro configurados');
    }
}

module.exports = ErrorHandler;
