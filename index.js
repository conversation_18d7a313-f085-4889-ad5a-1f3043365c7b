const whatsapp = require('./src/services/whatsapp');
const conversation = require('./src/core/conversation');
const fileManager = require('./src/utils/fileManager');
const { conversationHistoryFile, INTERVALS } = require('./src/config/config');
const { saveStats } = require('./src/utils/eventTracker');
const healthMonitor = require('./src/utils/healthMonitor');
const ErrorHandler = require('./src/utils/errorHandler');

// Configura handlers globais de erro centralizados
ErrorHandler.setupGlobalHandlers();

// Inicialização do sistema
(async () => {
    try {
        console.log('🚀 Iniciando ZapIA Bot...');

        // Inicializa estruturas necessárias usando fileManager centralizado
        await fileManager.ensureFileAndDirectoryExists(
            conversationHistoryFile,
            []
        );

        // Carregar histórico de conversas
        await conversation.loadHistory();

        // Iniciar limpeza periódica
        fileManager.scheduleCleanup();

        // Inicia o monitor de saúde
        healthMonitor.start();

        // Conectar ao WhatsApp
        await whatsapp.connect();
        console.log('✅ Bot iniciado com sucesso');

        // Inicia salvamento periódico de estatísticas usando configuração centralizada
        setInterval(saveStats, INTERVALS.STATS_SAVE);

        // Atualiza o heartbeat usando configuração centralizada
        setInterval(() => healthMonitor.updateHeartbeat(), INTERVALS.HEARTBEAT);
    } catch (error) {
        ErrorHandler.handleCritical(error, 'Initialization', true);
    }
})();
