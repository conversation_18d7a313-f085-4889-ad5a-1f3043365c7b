# Environment and Configuration
.env
.env.*
auth/

# Dependencies
node_modules/
package-lock.json
yarn.lock

# Data and Cache
data/
*.pid
*.seed
*.pid.lock
.npm
.eslintcache

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test Coverage
coverage/

# System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDEs and Editors
.idea/
.vscode/
*.swp
*.swo
*.swn
*.sublime-workspace
*.sublime-project

# Temporary Files
*.tmp
*.temp
*.bak
*.bkp

# Build outputs
dist/
build/
out/

# Debug files
debug/
.debug
